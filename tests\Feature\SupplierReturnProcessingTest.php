<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\SupplierDelivery;
use App\Models\ReturnModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SupplierReturnProcessingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $supplier;
    protected $supplierUser;
    protected $product;
    protected $supplierDelivery;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Create supplier
        $this->supplier = Supplier::factory()->create([
            'name' => 'Test Supplier',
            'status' => 'active',
        ]);

        // Create supplier user
        $this->supplierUser = User::factory()->create([
            'role' => 'supplier',
            'supplier_id' => $this->supplier->id,
            'email' => '<EMAIL>',
        ]);

        // Create product
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'sku' => 'TEST-001',
        ]);

        // Create supplier delivery
        $this->supplierDelivery = SupplierDelivery::factory()->create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'received_quantity' => 90, // 10 units short
            'status' => 'partial',
            'delivery_date' => now(),
            'received_date' => now(),
            'received_by' => $this->admin->id,
        ]);
    }

    /** @test */
    public function it_calculates_max_returnable_quantity_correctly_before_any_returns()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));

        $response->assertStatus(200);
        
        // Max returnable should be: sent_quantity (100) - received_quantity (90) = 10
        $delivery = $response->viewData('delivery');
        $this->assertEquals(10, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_reduces_max_returnable_quantity_after_supplier_accepts_return()
    {
        // Create a warehouse-to-supplier return
        $return = ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 5,
            'reason' => 'damaged',
            'description' => 'Test return',
            'status' => 'approved',
            'return_date' => now(),
            'approved_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Supplier accepts the return (status changes to in_transit)
        $this->actingAs($this->supplierUser);
        $response = $this->post(route('supplier.returns.respond', $return), [
            'action' => 'accept',
            'supplier_notes' => 'Return accepted',
        ]);

        $response->assertRedirect(route('supplier.returns.index'));
        
        // Verify return status changed to in_transit
        $return->refresh();
        $this->assertEquals('in_transit', $return->status);

        // Check max returnable quantity is reduced
        $this->actingAs($this->admin);
        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));
        
        $delivery = $response->viewData('delivery');
        // Max returnable should now be: sent_quantity (100) - received_quantity (90) - accepted_returns (5) = 5
        $this->assertEquals(5, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_accounts_for_processed_returns_with_accepted_action()
    {
        // Create a return and move it to history with accepted action
        $return = ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 8,
            'reason' => 'defective',
            'description' => 'Test processed return',
            'status' => 'completed',
            'return_date' => now(),
            'approved_date' => now(),
            'completed_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Move to history with accepted action
        $return->moveToHistory('accepted', 'Return processed and accepted', $this->admin->id);

        // Check max returnable quantity accounts for processed return
        $this->actingAs($this->admin);
        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));
        
        $delivery = $response->viewData('delivery');
        // Max returnable should be: sent_quantity (100) - received_quantity (90) - processed_accepted_returns (8) = 2
        $this->assertEquals(2, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_prevents_creating_return_exceeding_max_returnable_quantity()
    {
        // Create a return that uses up most of the returnable quantity
        ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 8,
            'reason' => 'damaged',
            'description' => 'First return',
            'status' => 'in_transit', // Accepted by supplier
            'return_date' => now(),
            'approved_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Try to create another return that would exceed max returnable quantity
        $this->actingAs($this->admin);
        $response = $this->post(route('admin.returns.store'), [
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'quantity' => 5, // This would exceed the remaining 2 units
            'reason' => 'defective',
            'description' => 'Second return - should fail',
            'return_date' => now()->format('Y-m-d'),
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['quantity']);
        
        // Verify the error message mentions the correct max returnable quantity
        $errors = session('errors');
        $this->assertStringContains('tidak boleh melebihi 2 unit', $errors->first('quantity'));
    }
}
